import baseUrl from './base-url'

// Helper function to convert blob to data URL
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onloadend = () => resolve(reader.result as string)
    reader.readAsDataURL(blob)
  })
}

export async function fetchImageAsDataUrl(imagePath: string): Promise<string> {
  const response = await fetch(`${baseUrl}/image/medium/${imagePath}`)
  const blob = await response.blob()
  return blobToDataUrl(blob)
}
