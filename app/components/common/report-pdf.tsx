/* eslint-disable react/no-array-index-key */
import type { HtmlElement } from 'node_modules/react-pdf-html/dist/types/parse'
import type { GetPdfSettingQuery, TestReport } from '~/gql/graphql'
import type { DeepPartial } from '~/lib/types/deep-partial'
import {
  Document,
  Image,
  Page,
  StyleSheet,
  Text,
  View,
} from '@react-pdf/renderer'
import { format } from 'date-fns'
import { useCallback, useEffect, useState } from 'react'
import Html from 'react-pdf-html'
import { fetchImageAsDataUrl } from '~/lib/fetch-image-as-data-url'

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontSize: 10, // Default text size
    position: 'relative', // Added for absolute positioning of footer
  },
  patientSection: { flexDirection: 'row', marginBottom: 15 },
  patientInfoColumn: { flex: 1, flexDirection: 'column', gap: 4 },
  patientDataRow: { flexDirection: 'row' },
  patientLabelCell: {
    flexBasis: '35%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingRight: 5,
  },
  patientValueCell: { flexBasis: '65%', flexWrap: 'wrap' },
  dateLabelCell: {
    flexBasis: '65%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingRight: 5,
  },
  dateValueCell: { flexBasis: '35%', flexWrap: 'wrap' },
  boldText: { fontWeight: 'bold' },

  reportTitle: {
    fontSize: 20,
    textAlign: 'center',
    marginVertical: 15,
    fontWeight: 'bold',
  },

  sectionCard: {
    // marginBottom: 12,
    // borderWidth: 1,
    // borderColor: '#E5E7EB',
    // padding: 8,
    display: 'flex',
    // flex: 1,
    flexDirection: 'column',
    marginBottom: 32,
    padding: 8,
  }, // Changed border: 1 to borderWidth: 1
  sectionTitle: { fontSize: 16, fontWeight: 'bold', marginBottom: 8 },

  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#374151',
    paddingBottom: 4,
    marginBottom: 4,
  }, // Darker gray border
  tableHeaderText: { fontWeight: 'bold', fontSize: 10 }, // Explicitly 'bold' for PDF, was semibold

  thCol4: { flexBasis: '33.33%' },
  thCol3: { flexBasis: '25%' },
  thCol2: { flexBasis: '16.66%' },
  thCol1: { flexBasis: '8.33%' },
  thColFull: { flexBasis: '66.66%', width: '100%' },

  tableRow: {
    flexDirection: 'row',
    paddingVertical: 3,
    borderBottomWidth: 0.5,
    borderBottomColor: '#F3F4F6',
  }, // Very light border for rows

  tdCol4: { flexBasis: '33.33%', paddingRight: 2 },
  tdCol3: { flexBasis: '25%', paddingRight: 2 },
  tdCol2: { flexBasis: '16.66%', paddingRight: 2 },
  tdCol1: { flexBasis: '8.33%', paddingRight: 2 },
  tdColFull: { flexBasis: '75%', paddingRight: 2 },

  paddingLeft20: { paddingLeft: 20 },
  subfieldBlock: { marginTop: 3 },
  subfieldName: { fontWeight: 'bold', marginBottom: 2 }, // Was medium, making bold for clarity
  subfieldRow: { flexDirection: 'row', paddingVertical: 1 },
  subfieldValueCell: { flexBasis: '66.66%' }, // Added for alignment with label
  bottomTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'absolute', // Position at the bottom
    bottom: 30, // Adjust padding from bottom
    left: 30, // Adjust padding from left
    right: 30, // Adjust padding from right
  },
  textCenter: { textAlign: 'center' },
  textRight: { textAlign: 'right' },

  imageBlock: { width: 50, height: 50, position: 'absolute', top: 0, left: 0 },
  headerSection: {
    display: 'flex',
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    marginBottom: 15,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },

  titleBlock: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    alignItems: 'center',
    width: '100%',
    flex: 1,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    width: '100%',
    textAlign: 'center',
  },
  subtitle: { width: '100%', textAlign: 'center', whiteSpace: 'pre-wrap' },
  reference: { whiteSpace: 'pre-wrap' },
  footerContainer: {
    display: 'flex',
    flexDirection: 'row',
    whiteSpace: 'pre-wrap',
  },
  bottomFooterLeft: {
    textAlign: 'left',
    whiteSpace: 'pre-wrap',
    basis: '33.33%',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  bottomFooterCenter: {
    whiteSpace: 'pre-wrap',
    height: '100%',
    basis: '33.33%',
    width: '100%',
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  bottomFooterLeftContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    width: '100%',
    basis: '33.33%',
  },
  bottomFooterRightContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    width: '100%',
    basis: '33.33%',
  },
  bottomFooterRight: {
    textAlign: 'right',
    whiteSpace: 'pre-wrap',
    basis: '33.33%',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  signatureBlock: {
    position: 'relative',
    width: '100%',
  },
  signatureImage: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    height: 50,
    width: 50,
  },
})

const htmlStylesheet = {
  'p': {
    fontSize: 10,
  },
  'li': {
    paddingLeft: 8,
    paddingTop: 4,
  },
  'li > p': {
    fontSize: 10,
  },
}

interface Props {
  reportData: DeepPartial<TestReport>
  // imgData?: string
  pdfSettings: GetPdfSettingQuery['getPdfSetting'] | null | undefined
  hideEmptyFields?: boolean
}

export default function ReportPDFDocument({
  reportData,
  // imgData,
  pdfSettings,
  hideEmptyFields = false,
}: Props) {
  const { patient, template_data, template } = reportData

  const [imageData, setImageData] = useState<{
    logo?: string
    leftSignature?: string
    rightSignature?: string
  }>({})

  const loadImages = useCallback(async () => {
    if (!pdfSettings) {
      return
    }

    try {
      const imagePromises: Promise<[string, string]>[] = []

      // Collect all image paths that need to be fetched
      if (pdfSettings.logo?.path) {
        imagePromises.push(
          fetchImageAsDataUrl(pdfSettings.logo.path).then(dataUrl => ['logo', dataUrl] as [string, string]),
        )
      }

      if (pdfSettings.leftSignature?.path) {
        imagePromises.push(
          fetchImageAsDataUrl(pdfSettings.leftSignature.path).then(dataUrl => ['leftSignature', dataUrl] as [string, string]),
        )
      }

      if (pdfSettings.rightSignature?.path) {
        imagePromises.push(
          fetchImageAsDataUrl(pdfSettings.rightSignature.path).then(dataUrl => ['rightSignature', dataUrl] as [string, string]),
        )
      }

      // Fetch all images concurrently
      const results = await Promise.all(imagePromises)

      // Convert results to object
      const newImageData = results.reduce((acc, [key, dataUrl]) => {
        acc[key as keyof typeof acc] = dataUrl
        return acc
      }, {} as typeof imageData)

      setImageData(newImageData)
    }
    catch (error) {
      console.error('Error loading images:', error)
      setImageData({})
    }
  }, [pdfSettings])

  useEffect(() => {
    loadImages()
  }, [loadImages])

  // Helper function to check if a value is empty
  const isEmpty = (value: any): boolean => {
    return value === null || value === undefined || value === '' || value === 0
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.headerSection}>
          {imageData.logo
            ? (
                <View style={styles.imageBlock}>
                  <Image src={imageData.logo} />
                </View>
              )
            : null}
          <View style={styles.titleBlock}>
            <Text style={[styles.title, { color: template?.title_color }]}>
              {pdfSettings?.title}
            </Text>
            <Text style={styles.subtitle}>{pdfSettings?.subtitle}</Text>
          </View>
        </View>

        {/* Patient Information */}
        <View style={styles.patientSection}>
          <View style={styles.patientInfoColumn}>
            {(!hideEmptyFields || !isEmpty(patient?.name)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.patientLabelCell}>
                  <Text style={styles.boldText}>Name</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.patientValueCell}>
                  <Text>{patient?.name}</Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(patient?.age)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.patientLabelCell}>
                  <Text style={styles.boldText}>Age</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.patientValueCell}>
                  <Text>{patient?.age}</Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(patient?.gender)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.patientLabelCell}>
                  <Text style={styles.boldText}>Gender</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.patientValueCell}>
                  <Text>{patient?.gender}</Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(patient?.phone_number)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.patientLabelCell}>
                  <Text style={styles.boldText}>Phone</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.patientValueCell}>
                  <Text>{patient?.phone_number}</Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(patient?.address)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.patientLabelCell}>
                  <Text style={styles.boldText}>Address</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.patientValueCell}>
                  <Text>{patient?.address}</Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(patient?.blood_group)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.patientLabelCell}>
                  <Text style={styles.boldText}>Blood Group</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.patientValueCell}>
                  <Text>{patient?.blood_group}</Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(reportData?.doctor?.name)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.patientLabelCell}>
                  <Text style={styles.boldText}>Ref. Doctor</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.patientValueCell}>
                  <Text>{reportData?.doctor?.name}</Text>
                </View>
              </View>
            )}
          </View>

          <View style={styles.patientInfoColumn}>
            {(!hideEmptyFields || !isEmpty(reportData.collection_date)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.dateLabelCell}>
                  <Text style={styles.boldText}>Collection Date</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.dateValueCell}>
                  <Text>
                    {reportData.collection_date
                      ? format(reportData.collection_date, 'dd-MMM-yyyy')
                      : ''}
                  </Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(reportData.test_date)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.dateLabelCell}>
                  <Text style={styles.boldText}>Test Date</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.dateValueCell}>
                  <Text>
                    {reportData.test_date
                      ? format(reportData.test_date, 'dd-MMM-yyyy')
                      : ''}
                  </Text>
                </View>
              </View>
            )}
            {(!hideEmptyFields || !isEmpty(reportData.report_generation_date)) && (
              <View style={styles.patientDataRow}>
                <View style={styles.dateLabelCell}>
                  <Text style={styles.boldText}>Report Generation Date</Text>
                  <Text>:</Text>
                </View>
                <View style={styles.dateValueCell}>
                  <Text>
                    {reportData.report_generation_date
                      ? format(reportData.report_generation_date, 'dd-MMM-yyyy')
                      : ''}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        <Text
          style={[
            styles.reportTitle,
            { color: template?.report_name_color },
          ]}
        >
          {template?.name}
        </Text>

        {template_data?.sections?.map((section, index) => (
          <View key={index} style={styles.sectionCard}>
            {/* <Text style={styles.sectionTitle}>{section?.name}</Text> */}
            <View style={styles.tableHeader}>
              <Text style={[styles.thCol3, styles.tableHeaderText]}>
                {section?.name}
              </Text>
              <Text style={[styles.thCol3, styles.tableHeaderText]}>
                Result
              </Text>
              <Text style={[styles.thCol2, styles.tableHeaderText]}>Unit</Text>
              <Text style={[styles.thCol3, styles.tableHeaderText]}>
                BIO.REF.INTERVAL
              </Text>
            </View>
            {section?.fields?.map((field, fieldIndex) => {
              // For non-subfield types, hide if hideEmptyFields is true and name_value is empty
              if (field?.input_type !== 'subfield' && hideEmptyFields && isEmpty(field?.name_value)) {
                return null
              }
              return (
                <View key={fieldIndex}>
                  {field?.input_type === 'subfield'
                    ? (
                        <View style={styles.subfieldBlock}>
                          <Text style={styles.subfieldName}>{field.name}</Text>
                          {field.sub_fields?.map((subField, subFieldIndex) => {
                          // Hide subfield if hideEmptyFields is true and name_value is empty
                            if (hideEmptyFields && isEmpty(subField?.name_value)) {
                              return null
                            }
                            return (
                              <View key={subFieldIndex} style={styles.subfieldRow}>
                                <Text style={[styles.tdCol3, styles.paddingLeft20]}>
                                  {subField?.name}
                                </Text>
                                <Text style={styles.tdCol3}>
                                  {subField?.name_value}
                                </Text>
                                <Text style={styles.tdCol2}>
                                  {subField?.unit_value}
                                </Text>
                                <Text style={[styles.tdCol3, styles.reference]}>
                                  {subField?.reference_value}
                                </Text>
                              </View>
                            )
                          })}
                        </View>
                      )
                    : (
                        <View style={styles.tableRow}>
                          <Text style={styles.tdCol3}>{field?.name}</Text>
                          <View
                            style={
                              !field?.unit_active && !field?.reference_active && field?.input_type === 'richtext'
                                ? styles.tdColFull
                                : styles.tdCol3
                            }
                          >
                            {field?.input_type === 'multiselect'
                              ? (
                                  <Text>{field.input_type_values?.join(', ')}</Text>
                                )
                              : field?.input_type === 'richtext'
                                ? (
                                    <Html
                                      renderers={{
                                        li: ({ element, children }) => {
                                          const list = element.closest(
                                            'ol, ul',
                                          ) as HtmlElement
                                          const isOrderedList
                                = list?.tag === 'ol'
                                  || element.parentNode.tag === 'ol'

                                          return (
                                            <View
                                              wrap={false}
                                              style={{
                                                display: 'flex',
                                                flexDirection: 'row',
                                                fontSize: 10,
                                                marginTop: 4,
                                              }}
                                            >
                                              <View
                                                style={{
                                                  marginRight: 4,
                                                  width: 8, // Fixed width for numbers
                                                  alignItems: 'flex-end', // Right-align numbers
                                                }}
                                              >
                                                <Text
                                                  style={{
                                                    fontSize: 10, // Smaller font for numbers
                                                    fontWeight: isOrderedList
                                                      ? 'normal'
                                                      : 'bold', // Normal weight for numbers
                                                  }}
                                                >
                                                  {isOrderedList
                                                    ? `${element.indexOfType + 1}.`
                                                    : '•'}
                                                </Text>
                                              </View>
                                              <View style={{ flex: 1 }}>{children}</View>
                                            </View>
                                          )
                                        },
                                      }}
                                      resetStyles
                                      stylesheet={htmlStylesheet}
                                    >
                                      {field?.name_value || ''}
                                    </Html>
                                  )
                                : (
                                    <Text>{field?.name_value}</Text>
                                  )}
                          </View>
                          {field?.unit_active
                            ? (
                                <Text style={styles.tdCol2}>{field?.unit_value}</Text>
                              )
                            : field?.input_type !== 'richtext' ? <View style={styles.tdCol2} /> : null}
                          {field?.reference_active
                            ? (
                                <Text style={[styles.tdCol3, styles.reference]}>
                                  {field?.reference_value}
                                </Text>
                              )
                            : field?.input_type !== 'richtext' ? <View style={styles.tdCol3} /> : null}
                        </View>
                      )}
                </View>
              )
            })}
          </View>
        ))}

        <View style={{
          display: 'flex',
          flex: 1,
        }}
        />

        {/* Bottom Text Blocks */}
        <View style={styles.footerContainer}>
          <View style={styles.bottomFooterLeftContainer}>
            {imageData.leftSignature
              ? (
                  <View style={styles.signatureBlock}>
                    <Image src={imageData.leftSignature} style={styles.signatureImage} />
                  </View>
                )
              : null}
            <View style={styles.bottomFooterLeft}>
              <Text>{pdfSettings?.footer_left}</Text>
            </View>
          </View>

          <View style={styles.bottomFooterCenter}>
            <Text>
              {pdfSettings?.footer_center}
            </Text>
          </View>

          <View style={styles.bottomFooterRightContainer}>
            {imageData.rightSignature
              ? (
                  <View style={styles.signatureBlock}>
                    <Image src={imageData.rightSignature} style={styles.signatureImage} />
                  </View>
                )
              : null}
            <View style={styles.bottomFooterRight}>
              <Text>{pdfSettings?.footer_right}</Text>
            </View>
          </View>
        </View>
      </Page>
    </Document>
  )
}
